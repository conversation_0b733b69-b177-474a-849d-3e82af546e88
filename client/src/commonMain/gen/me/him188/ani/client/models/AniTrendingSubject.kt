// @formatter:off
/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package me.him188.ani.client.models


import kotlinx.serialization.*
import kotlinx.serialization.descriptors.*
import kotlinx.serialization.encoding.*

/**
 * 
 *
 * @param bangumiId 
 * @param nameCn 
 * @param imageLarge 
 */
@Serializable

data class AniTrendingSubject (

    @SerialName(value = "bangumiId") @Required val bangumiId: kotlin.Int,

    @SerialName(value = "nameCn") @Required val nameCn: kotlin.String,

    @SerialName(value = "imageLarge") @Required val imageLarge: kotlin.String

) {


}


// @formatter:on
