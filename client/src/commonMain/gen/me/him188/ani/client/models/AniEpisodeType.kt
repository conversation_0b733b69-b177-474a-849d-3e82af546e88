/*
 * Copyright (C) 2024-2025 OpenAni and contributors.
 *
 * 此源代码的使用受 GNU AFFERO GENERAL PUBLIC LICENSE version 3 许可证的约束, 可以在以下链接找到该许可证.
 * Use of this source code is governed by the GNU AGPLv3 license, which can be found at the following link.
 *
 * https://github.com/open-ani/ani/blob/main/LICENSE
 */

// @formatter:off
/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package me.him188.ani.client.models


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 
 *
 * Values: MAIN,SPECIAL,OP,ED,TRAILER,MAD,OTHER
 */
@Serializable
enum class AniEpisodeType(val value: kotlin.String) {

    @SerialName(value = "MAIN")
    MAIN("MAIN"),

    @SerialName(value = "SPECIAL")
    SPECIAL("SPECIAL"),

    @SerialName(value = "OP")
    OP("OP"),

    @SerialName(value = "ED")
    ED("ED"),

    @SerialName(value = "TRAILER")
    TRAILER("TRAILER"),

    @SerialName(value = "MAD")
    MAD("MAD"),

    @SerialName(value = "OTHER")
    OTHER("OTHER");

    /**
     * Override [toString()] to avoid using the enum variable name as the value, and instead use
     * the actual value defined in the API spec file.
     *
     * This solves a problem when the variable name and its value are different, and ensures that
     * the client sends the correct enum values to the server always.
     */
    override fun toString(): kotlin.String = value

    companion object {
        /**
         * Converts the provided [data] to a [String] on success, null otherwise.
         */
        fun encode(data: kotlin.Any?): kotlin.String? = if (data is AniEpisodeType) "$data" else null

        /**
         * Returns a valid [AniEpisodeType] for [data], null otherwise.
         */
        fun decode(data: kotlin.Any?): AniEpisodeType? = data?.let {
          val normalizedData = "$it".lowercase()
          values().firstOrNull { value ->
            it == value || normalizedData == "$value".lowercase()
          }
        }
    }
}


// @formatter:on
