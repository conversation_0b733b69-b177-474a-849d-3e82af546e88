package me.him188.ani.app.ui.foundation.dialogs

import androidx.compose.ui.window.DialogProperties

@Suppress("FunctionName")
actual fun PlatformDialogPropertiesImpl(
    dismissOnBackPress: <PERSON><PERSON><PERSON>,
    dismissOnClickOutside: <PERSON><PERSON><PERSON>,
    usePlatformDefaultWidth: <PERSON><PERSON><PERSON>,
    excludeFromSystemGesture: <PERSON>olean,
    usePlatformInsets: Boolean,
): DialogProperties {
    return DialogProperties(
        dismissOnBackPress = dismissOnBackPress,
        dismissOnClickOutside = dismissOnClickOutside,
        usePlatformDefaultWidth = usePlatformDefaultWidth,
    )
}