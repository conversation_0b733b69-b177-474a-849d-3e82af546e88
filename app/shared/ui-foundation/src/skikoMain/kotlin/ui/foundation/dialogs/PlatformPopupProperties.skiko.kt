package me.him188.ani.app.ui.foundation.dialogs

import androidx.compose.ui.window.PopupProperties

@Suppress("FunctionName")
actual fun PlatformPopupPropertiesImpl(
    focusable: <PERSON><PERSON><PERSON>,
    dismissOnBackPress: <PERSON><PERSON><PERSON>,
    dismissOnClickOutside: <PERSON><PERSON>an,
    usePlatformDefaultWidth: Boolean,
    // Android-only:
    excludeFromSystemGesture: Boolean,
    clippingEnabled: Boolean,
    // Desktop-only:
    usePlatformInsets: Boolean,
): PopupProperties {
    return PopupProperties(
        focusable = focusable,
        dismissOnBackPress = dismissOnBackPress,
        dismissOnClickOutside = dismissOnClickOutside,
        clippingEnabled = clippingEnabled,
        usePlatformDefaultWidth = usePlatformDefaultWidth,
        usePlatformInsets = usePlatformInsets,
    )
}