/*
 * Copyright (C) 2024-2025 OpenAni and contributors.
 *
 * 此源代码的使用受 GNU AFFERO GENERAL PUBLIC LICENSE version 3 许可证的约束, 可以在以下链接找到该许可证.
 * Use of this source code is governed by the GNU AGPLv3 license, which can be found at the following link.
 *
 * https://github.com/open-ani/ani/blob/main/LICENSE
 */

// @formatter:off
@file:Suppress("RedundantVisibilityModifier")

// Generated by me.him188.ani.utils.bbcode.BBCodeTestGenerator
package me.him188.ani.utils.bbcode

import kotlin.test.Test

public class GenBBBasicsTest : BBCodeParserTestHelper() {
    @Test
    public fun parse137138206() {
        BBCode.parse("[b]Hello World![/b]")
        .run {
            assertText(elements.at(0), value="Hello World!", bold=true)
        }
    }

    @Test
    public fun parse398420167() {
        BBCode.parse("[url]https://example.com[/url]")
        .run {
            assertText(elements.at(0), value="https://example.com", jumpUrl="https://example.com")
        }
    }

    @Test
    public fun parse1452225337() {
        BBCode.parse("[URL]https://example.com[/URL]")
        .run {
            assertText(elements.at(0), value="https://example.com", jumpUrl="https://example.com")
        }
    }

    @Test
    public fun parse181699913() {
        BBCode.parse("[url=https://example.com]Hello World![/url]")
        .run {
            assertText(elements.at(0), value="Hello World!", jumpUrl="https://example.com")
        }
    }

    @Test
    public fun parse307708920() {
        BBCode.parse("[url=http://example.com]Hello World![/url]")
        .run {
            assertText(elements.at(0), value="Hello World!", jumpUrl="http://example.com")
        }
    }

    @Test
    public fun parse1856624834() {
        BBCode.parse("[url=\"http://example.com\"]Hello World![/url]")
        .run {
            assertText(elements.at(0), value="Hello World!", jumpUrl="http://example.com")
        }
    }

    @Test
    public fun parse611450064() {
        BBCode.parse("[url=\"http://example.com\\n\"]Hello World![/url]")
        .run {
            assertText(elements.at(0), value="Hello World!", jumpUrl="http://example.com\\n")
        }
    }

    @Test
    public fun parse2136488827() {
        BBCode.parse("[url=invalidurl]Hello World![/url]")
        .run {
            assertText(elements.at(0), value="Hello World!", jumpUrl="invalidurl")
        }
    }

    @Test
    public fun parse1438076936() {
        BBCode.parse("[URL=http://example.com]Hello World![/URL]")
        .run {
            assertText(elements.at(0), value="Hello World!", jumpUrl="http://example.com")
        }
    }

    @Test
    public fun parse556838779() {
        BBCode.parse("[URL=invalidurl]Hello World![/URL]")
        .run {
            assertText(elements.at(0), value="Hello World!", jumpUrl="invalidurl")
        }
    }

    @Test
    public fun parse220807058() {
        BBCode.parse("[img]http://example.com[/img]")
        .run {
            assertImage(elements.at(0), imageUrl="http://example.com")
        }
    }

    @Test
    public fun parse1935298734() {
        BBCode.parse("[IMG]http://example.com[/IMG]")
        .run {
            assertImage(elements.at(0), imageUrl="http://example.com")
        }
    }

    @Test
    public fun parse1432337066() {
        BBCode.parse("[img=300,200]https://example.com/image.png[/img]")
        .run {
            assertImage(elements.at(0), imageUrl="https://example.com/image.png", width=300,
                    height=200)
        }
    }

    @Test
    public fun parse321382346() {
        BBCode.parse("[IMG=640,480]https://example.com/pic.jpg[/IMG]")
        .run {
            assertImage(elements.at(0), imageUrl="https://example.com/pic.jpg", width=640,
                    height=480)
        }
    }

    @Test
    public fun parse732922532() {
        BBCode.parse("[img=300]https://example.com/image.png[/img]")
        .run {
            assertImage(elements.at(0), imageUrl="https://example.com/image.png")
        }
    }

    @Test
    public fun parse1876696639() {
        BBCode.parse("[img=,200]https://example.com/image.png[/img]")
        .run {
            assertImage(elements.at(0), imageUrl="https://example.com/image.png")
        }
    }

    @Test
    public fun parse1264724581() {
        BBCode.parse("[img=0,0]https://example.com/image.png[/img]")
        .run {
            assertImage(elements.at(0), imageUrl="https://example.com/image.png", width=0, height=0)
        }
    }

    @Test
    public fun parse47556228() {
        BBCode.parse("[IMG=300]https://example.com/image.png[/img]")
        .run {
            assertImage(elements.at(0), imageUrl="https://example.com/image.png")
        }
    }

    @Test
    public fun parse1648215583() {
        BBCode.parse("[IMG=,200]https://example.com/image.png[/img]")
        .run {
            assertImage(elements.at(0), imageUrl="https://example.com/image.png")
        }
    }

    @Test
    public fun parse579358277() {
        BBCode.parse("[IMG=0,0]https://example.com/image.png[/img]")
        .run {
            assertImage(elements.at(0), imageUrl="https://example.com/image.png", width=0, height=0)
        }
    }

    @Test
    public fun parse1690130686() {
        BBCode.parse("[size=1]Hello World![/size]")
        .run {
            assertText(elements.at(0), value="Hello World!", size=1)
        }
    }

    @Test
    public fun parse1924176660() {
        BBCode.parse("[color=red]Hello World![/color]")
        .run {
            assertText(elements.at(0), value="Hello World!", color="red")
        }
    }

    @Test
    public fun parse1550990581() {
        BBCode.parse("[color=#AFAFAF]Hello World![/color]")
        .run {
            assertText(elements.at(0), value="Hello World!", color="#AFAFAF")
        }
    }

    @Test
    public fun parse1360880245() {
        BBCode.parse("[color=#AFAFAFFF]Hello World![/color]")
        .run {
            assertText(elements.at(0), value="Hello World!", color="#AFAFAFFF")
        }
    }

    @Test
    public fun parse323759858() {
        BBCode.parse("(=v=) Hello World! (-w=)")
        .run {
            assertKanmoji(elements.at(0), id="(=v=)")
            assertText(elements.at(1), value=" Hello World! ")
            assertKanmoji(elements.at(2), id="(-w=)")
        }
    }

    @Test
    public fun parse682053775() {
        BBCode.parse("(bgm123) Hello World! (bgm2)")
        .run {
            assertBangumiSticker(elements.at(0), id=123)
            assertText(elements.at(1), value=" Hello World! ")
            assertBangumiSticker(elements.at(2), id=2)
        }
    }

    @Test
    public fun parse250499401() {
        BBCode.parse("(bgm 2)")
        .run {
            assertText(elements.at(0), value="(bgm")
            assertText(elements.at(1), value=" 2)")
        }
    }

    @Test
    public fun parse1238845362() {
        BBCode.parse("(bgm 2")
        .run {
            assertText(elements.at(0), value="(bgm")
            assertText(elements.at(1), value=" 2")
        }
    }

    @Test
    public fun parse1967962848() {
        BBCode.parse("Hello (bgm 2")
        .run {
            assertText(elements.at(0), value="Hello ")
            assertText(elements.at(1), value="(bgm")
            assertText(elements.at(2), value=" 2")
        }
    }

    @Test
    public fun parse394436571() {
        BBCode.parse("Hello(=v=)")
        .run {
            assertText(elements.at(0), value="Hello")
            assertKanmoji(elements.at(1), id="(=v=)")
        }
    }
}


// @formatter:on
