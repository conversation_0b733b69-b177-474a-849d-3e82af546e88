package dev.hikari.xlogdecoder

import java.io.*
import java.nio.ByteBuffer
import java.nio.ByteOrder

object XLogFileDecoder {
    var MAGIC_NO_COMPRESS_START: Byte = 0x03
    var MAGIC_NO_COMPRESS_START1: Byte = 0x06
    var MAGIC_NO_COMPRESS_NO_CRYPT_START: Byte = 0x08
    var MAGIC_COMPRESS_START: Byte = 0x04
    var MAGIC_COMPRESS_START1: Byte = 0x05
    var MAGIC_COMPRESS_START2: Byte = 0x07
    var MAGIC_COMPRESS_NO_CRYPT_START: Byte = 0x09
    var MAGIC_END: Byte = 0x00

    //TODO using your own private key
    var PRIV_KEY = ""

    //TODO: using your own public key
    var PUB_KEY = ""

    //public static byte[] BYTE_PRIV_KEY = dev.hikari.xlogdecoder.CommonUtils.hexStringToBytes(PRIV_KEY);
    init {
        ECDHUtils.init()
    }

    private fun IsGoodLogBuffer(_buffer: ByteArray, _offset: Int, count: Int): Boolean {
        if (_offset == _buffer.size) return true
        val crypt_key_len: Int
        val magic_start = _buffer[_offset]
        crypt_key_len =
            if (MAGIC_NO_COMPRESS_START == magic_start || MAGIC_COMPRESS_START == magic_start || MAGIC_COMPRESS_START1 == magic_start) {
                4
            } else if (MAGIC_COMPRESS_START2 == magic_start || MAGIC_NO_COMPRESS_START1 == magic_start || MAGIC_NO_COMPRESS_NO_CRYPT_START == magic_start || MAGIC_COMPRESS_NO_CRYPT_START == magic_start) {
                64
            } else {
                println(String.format("_buffer[%d]:%d != MAGIC_NUM_START", _offset, _buffer[_offset]))
                return false
            }
        val headerLen = 1 + 2 + 1 + 1 + 4 + crypt_key_len
        if (_offset + headerLen + 1 + 1 > _buffer.size) {
            println(String.format("offset:%d > len(buffer):%d", _offset, _buffer.size))
            return false
        }
        val length =
            ByteBuffer.wrap(_buffer, _offset + headerLen - 4 - crypt_key_len, 4).order(ByteOrder.LITTLE_ENDIAN).getInt()
        val llength = length.toLong() and 0xffffffffL
        if (_offset + headerLen + llength + 1 > _buffer.size) {
            println(
                String.format(
                    "log length:%d, end pos %d > len(buffer):%d",
                    llength,
                    _offset + headerLen + llength + 1,
                    _buffer.size
                )
            )
            return false
        }
        if (MAGIC_END != _buffer[_offset + headerLen + length]) {
            println(
                String.format(
                    "log length:%d, buffer[%d]:%d != MAGIC_END",
                    length,
                    _offset + headerLen + length,
                    _buffer[_offset + headerLen + length]
                )
            )
            return false
        }
        return if (1 >= count) {
            true
        } else {
            IsGoodLogBuffer(_buffer, _offset + headerLen + length + 1, count - 1)
        }
    }

    private fun GetLogStartPos(_buffer: ByteArray, _count: Int): Int {
        var offset = 0
        while (true) {
            if (offset >= _buffer.size) break
            if (MAGIC_NO_COMPRESS_START == _buffer[offset] || MAGIC_NO_COMPRESS_START1 == _buffer[offset] || MAGIC_COMPRESS_START == _buffer[offset] || MAGIC_COMPRESS_START1 == _buffer[offset] || MAGIC_COMPRESS_START2 == _buffer[offset] || MAGIC_COMPRESS_NO_CRYPT_START == _buffer[offset] || MAGIC_NO_COMPRESS_NO_CRYPT_START == _buffer[offset]) {
                if (IsGoodLogBuffer(_buffer, offset, _count)) return offset
            }
            offset += 1
        }
        return -1
    }

    private fun DecodeBuffer(
        _buffer: ByteArray,
        _offset: Int,
        lastseq: Int,
        _outbuffer: StringBuffer,
        privateKey: String
    ): RetData {
        var _offset = _offset
        val retData = RetData(_offset, lastseq)
        if (_offset >= _buffer.size) return RetData(-1, lastseq)
        val ret = IsGoodLogBuffer(_buffer, _offset, 1)
        var tmpbuffer = ByteArray(_buffer.size - _offset)
        if (!ret) {
            System.arraycopy(_buffer, _offset, tmpbuffer, 0, tmpbuffer.size)
            val fixpos = GetLogStartPos(tmpbuffer, 1)
            _offset += if (-1 == fixpos) {
                return RetData(-1, lastseq)
            } else {
                _outbuffer.append(String.format("[F]decode_log_file.py decode error len=%d, result:%s \n", fixpos, ret))
                fixpos
            }
        }
        val magic_start = _buffer[_offset].toInt()
        val crypt_key_len: Int
        crypt_key_len =
            if (MAGIC_NO_COMPRESS_START.toInt() == magic_start || MAGIC_COMPRESS_START.toInt() == magic_start || MAGIC_COMPRESS_START1.toInt() == magic_start) {
                4
            } else if (MAGIC_COMPRESS_START2.toInt() == magic_start || MAGIC_NO_COMPRESS_START1.toInt() == magic_start || MAGIC_NO_COMPRESS_NO_CRYPT_START.toInt() == magic_start || MAGIC_COMPRESS_NO_CRYPT_START.toInt() == magic_start) {
                64
            } else {
                _outbuffer.append("in DecodeBuffer _buffer[%d]:%d != MAGIC_NUM_START", _offset, magic_start)
                return RetData(-1, lastseq)
            }
        val headerLen = 1 + 2 + 1 + 1 + 4 + crypt_key_len
        val length =
            ByteBuffer.wrap(_buffer, _offset + headerLen - 4 - crypt_key_len, 4).order(ByteOrder.LITTLE_ENDIAN).getInt()
        tmpbuffer = ByteArray(length)
        val seq =
            ByteBuffer.wrap(_buffer, _offset + headerLen - 4 - crypt_key_len - 2 - 2, 2).order(ByteOrder.LITTLE_ENDIAN)
                .getShort()
                .toInt() and 0x0FFFF
        val begin_hour = Char(
            ByteBuffer.wrap(_buffer, _offset + headerLen - 4 - crypt_key_len - 1 - 1, 1).order(ByteOrder.LITTLE_ENDIAN)
                .get().toUShort()
        )
        val end_hour = Char(
            ByteBuffer.wrap(_buffer, _offset + headerLen - 4 - crypt_key_len - 1, 1).order(ByteOrder.LITTLE_ENDIAN)
                .get().toUShort()
        )
        if (seq != 0 && seq != 1 && lastseq != 0 && seq != lastseq + 1) {
            _outbuffer.append(String.format("[F]decode_log_file.py log seq:%d-%d is missing\n", lastseq + 1, seq - 1))
        }
        if (seq != 0) {
            retData.lastseq = seq
        }
        System.arraycopy(_buffer, _offset + headerLen, tmpbuffer, 0, tmpbuffer.size)
        try {
            if (MAGIC_NO_COMPRESS_START1 == _buffer[_offset]) {
            } else if (MAGIC_COMPRESS_START2 == _buffer[_offset]) {
                val byte_pubkey_x = ByteArray(32)
                val byte_pubkey_y = ByteArray(32)
                ByteBuffer.wrap(_buffer, _offset + headerLen - crypt_key_len, crypt_key_len shr 1)
                    .order(ByteOrder.LITTLE_ENDIAN)[byte_pubkey_x]
                ByteBuffer.wrap(_buffer, _offset + headerLen - (crypt_key_len shr 1), crypt_key_len shr 1)
                    .order(ByteOrder.LITTLE_ENDIAN)[byte_pubkey_y]
                val pubkey_x = CommonUtils.bytesToHexString(byte_pubkey_x)
                val pubkey_y = CommonUtils.bytesToHexString(byte_pubkey_y)
                val pubkey = String.format("04%s%s", pubkey_x, pubkey_y)
                //byte[] tea_key = dev.hikari.xlogdecoder.ECDHUtils.GetECDHKey(dev.hikari.xlogdecoder.CommonUtils.hexStringToByteArray(pubkey), BYTE_PRIV_KEY);
                val tea_key =
                    ECDHUtils.GetECDHKey(
                        pubkey.chunked(2).map { it.toInt(16).toByte() }.toByteArray(),
                        CommonUtils.hexStringToBytes(privateKey)
                    )
                tmpbuffer = CommonUtils.tea_decrypt(tmpbuffer, tea_key)
                tmpbuffer = CommonUtils.decompress(tmpbuffer)
            } else if (MAGIC_COMPRESS_START == _buffer[_offset] ||
                MAGIC_COMPRESS_NO_CRYPT_START == _buffer[_offset]
            ) {
                tmpbuffer = CommonUtils.decompress(tmpbuffer)
            } else if (MAGIC_COMPRESS_START1 == _buffer[_offset]) {
            } else {
            }
        } catch (e: Exception) {
            e.printStackTrace()
            _outbuffer.append(String.format("[F]decode_log_file.py decompress err, %s\n", e.toString()))
            retData.startpos = _offset + headerLen + length + 1
            return retData
        }
        _outbuffer.append(String(tmpbuffer))
        retData.startpos = _offset + headerLen + length + 1
        return retData
    }

    fun ParseFile(_file: String?, _outfile: String?, privateKey: String = "") {
        var fis: FileInputStream? = null
        var dis: DataInputStream? = null
        var os: OutputStream? = null
        var writer: OutputStreamWriter? = null
        var bw: BufferedWriter? = null
        try {
            //创建输入流
            fis = FileInputStream(_file)
            dis = DataInputStream(fis)
            val _buffer = ByteArray(dis.available())
            dis.readFully(_buffer)
            val startpos = GetLogStartPos(_buffer, 2)
            if (-1 == startpos) {
                return
            }
            val outbuffer = StringBuffer()
            var retData = RetData(startpos, 0)
            while (true) {
                println(retData.startpos.toString() + ":" + retData.lastseq)
                retData = DecodeBuffer(_buffer, retData.startpos, retData.lastseq, outbuffer, privateKey)
                if (-1 == retData.startpos) break
            }
            if (0 == outbuffer.length) return
            os = FileOutputStream(_outfile)
            writer = OutputStreamWriter(os, Charsets.UTF_8)
            bw = BufferedWriter(writer)
            bw.write(outbuffer.toString())
            bw.flush()
        } catch (e: IOException) {
            e.printStackTrace()
        } finally {
            try {
                fis?.close()
                dis?.close()
                os?.close()
                writer?.close()
                bw?.close()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    fun convertStream(inStream: InputStream?, ost: OutputStream?, privateKey: String = "") {
        var dis: DataInputStream? = null
        var writer: OutputStreamWriter? = null
        var bw: BufferedWriter? = null
        try {
            //创建输入流
            dis = DataInputStream(inStream)
            val _buffer = ByteArray(dis.available())
            dis.readFully(_buffer)
            val startpos = GetLogStartPos(_buffer, 2)
            if (-1 == startpos) {
                return
            }
            val outbuffer = StringBuffer()
            var retData = RetData(startpos, 0)
            while (true) {
                println(retData.startpos.toString() + ":" + retData.lastseq)
                retData = DecodeBuffer(_buffer, retData.startpos, retData.lastseq, outbuffer, privateKey)
                if (-1 == retData.startpos) break
            }
            if (0 == outbuffer.length) return
            writer = OutputStreamWriter(ost)
            bw = BufferedWriter(writer)
            bw.write(outbuffer.toString())
            bw.flush()
        } catch (e: IOException) {
            e.printStackTrace()
        } finally {
            try {
                dis?.close()
                if (ost != null) {
                    ost.flush()
                    ost.close()
                }
                writer?.close()
                bw?.close()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    class RetData(var startpos: Int, var lastseq: Int)
}