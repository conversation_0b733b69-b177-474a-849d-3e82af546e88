package dev.hikari.xlogdecoder

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.clickable
import androidx.compose.foundation.draganddrop.dragAndDropTarget
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draganddrop.DragAndDropEvent
import androidx.compose.ui.draganddrop.DragAndDropTarget
import androidx.compose.ui.draganddrop.DragData
import androidx.compose.ui.draganddrop.dragData
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.*
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import net.harawata.appdirs.AppDirsFactory
import java.awt.event.WindowEvent
import java.awt.event.WindowFocusListener
import java.io.File
import java.net.URI
import java.util.*

fun main() = application {
    Window(
        onCloseRequest = ::exitApplication,
        title = "XLog Decoder",
        state = rememberWindowState(
            size = DpSize(360.dp, 320.dp),
            position = WindowPosition(Alignment.Center),
            placement = WindowPlacement.Floating
        ),
        resizable = false,
        alwaysOnTop = true,
    ) {
        val focusManager = LocalFocusManager.current
        DisposableEffect(Unit) {
            //窗口失焦时清除focus
            val listener = object : WindowFocusListener {
                override fun windowGainedFocus(e: WindowEvent?) = Unit
                override fun windowLostFocus(e: WindowEvent?) {
                    focusManager.clearFocus()
                }
            }
            window.addWindowFocusListener(listener)
            onDispose { window.removeWindowFocusListener(listener) }
        }
        App(focusManager)
    }
}

val userDataDir = File(AppDirsFactory.getInstance().getUserDataDir("XLogDecoder", null, "hikari"))
val dataStore = createDataStore { userDataDir.resolve("datastore") }

@OptIn(ExperimentalFoundationApi::class, ExperimentalComposeUiApi::class)
@Composable
fun App(focusManager: FocusManager) {
    val appVersion: String = Properties().run {
        load({}.javaClass.getResourceAsStream("/version.properties"))
        getProperty("version") ?: ""
    }

    val dirPath = remember { mutableStateOf("") }
    val dragAndDropTarget = remember {
        object : DragAndDropTarget {
            override fun onDrop(event: DragAndDropEvent): Boolean {
                val dragData = event.dragData()
                if (dragData !is DragData.FilesList) {
                    return false
                }
                dragData.readFiles().firstOrNull()?.let { uri ->
                    val file = File(URI(uri))
                    if (file.isFile) {
                        dirPath.value = file.parentFile.absolutePath
                    } else if (file.isDirectory) {
                        dirPath.value = file.absolutePath
                    }
                }
                return true
            }
        }
    }
    MaterialTheme {
        Column(
            modifier = Modifier.padding(8.dp)
                .clickable(indication = null, interactionSource = remember { MutableInteractionSource() }) {
                    focusManager.clearFocus()
                }
                .dragAndDropTarget(
                    shouldStartDragAndDrop = { true },
                    target = dragAndDropTarget
                ),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            val scope = rememberCoroutineScope()
            val privateKeyPrefKey = stringPreferencesKey("private_key")
            val deleteAfterDecodePrefKey = booleanPreferencesKey("delete_after_decode")

            var privateKey by rememberSaveable { mutableStateOf("") }
            LaunchedEffect(Unit) {
                dataStore.data
                    .map { it[privateKeyPrefKey] ?: "" }
                    .collect { privateKey = it }
            }

            val checkedState by dataStore.data
                .map { it[deleteAfterDecodePrefKey] ?: false }
                .collectAsState(false)

            OutlinedTextField(
                modifier = Modifier.fillMaxWidth()
                    .padding(16.dp, 0.dp)
                    .height(64.dp),
                value = dirPath.value,
                onValueChange = { dirPath.value = it },
                singleLine = true,
                label = { Text("日志文件目录") }
            )

            Divider(thickness = 6.dp, color = Color.Transparent)

            OutlinedTextField(
                modifier = Modifier.fillMaxWidth()
                    .padding(16.dp, 0.dp)
                    .height(64.dp),
                value = privateKey,
                onValueChange = {
                    privateKey = it
                    scope.launch {
                        dataStore.edit { prefs ->
                            prefs[privateKeyPrefKey] = it
                        }
                    }
                },
                singleLine = true,
                label = { Text("解密私钥") }
            )

            Divider(thickness = 6.dp, color = Color.Transparent)

            Row(
                modifier = Modifier
                    .clickable(
                        role = Role.Checkbox,
                        onClick = {
                            scope.launch {
                                dataStore.edit { prefs ->
                                    prefs[deleteAfterDecodePrefKey] = !checkedState
                                }
                            }
                        }
                    )
                    .padding(2.dp),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Checkbox(
                    checked = checkedState,
                    onCheckedChange = null
                )
                Text(
                    modifier = Modifier.padding(4.dp, 0.dp),
                    text = "解密后自动删除原文件"
                )
            }

            Divider(thickness = 6.dp, color = Color.Transparent)

            Box(
                modifier = Modifier.fillMaxWidth()
            ) {
                Button(
                    modifier = Modifier.align(Alignment.Center),
                    onClick = {
                        if (dirPath.value.isEmpty()) {
                            return@Button
                        }
                        File(dirPath.value).walk()
                            .maxDepth(1)
                            .forEach { file ->
                                if (file.isFile && file.name.endsWith(".xlog")) {
                                    XLogFileDecoder.ParseFile(file.absolutePath, file.absolutePath + ".log", privateKey)
                                    if (checkedState) {
                                        file.delete()
                                    }
                                }
                            }
                    }
                ) {
                    Text("确定", modifier = Modifier.padding(8.dp, 0.dp))
                }
                Text(
                    modifier = Modifier.align(Alignment.BottomEnd),
                    text = "v$appVersion@hikari",
                    fontSize = 12.sp
                )
            }
        }
    }
}
