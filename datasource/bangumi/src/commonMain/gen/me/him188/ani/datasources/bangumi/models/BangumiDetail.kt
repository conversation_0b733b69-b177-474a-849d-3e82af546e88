/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package me.him188.ani.datasources.bangumi.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 
 *
 * @param error error message
 * @param path request path
 */
@Serializable

data class BangumiDetail(

    /* error message */
    @SerialName(value = "error") val error: kotlin.String? = null,

    /* request path */
    @SerialName(value = "path") val path: kotlin.String? = null

) {


}

