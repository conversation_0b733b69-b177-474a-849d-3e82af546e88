/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport",
)

package me.him188.ani.datasources.bangumi.models

import kotlinx.serialization.Required
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 
 *
 * @param total 
 * @param limit 
 * @param offset 
 * @param `data` 
 */
@Serializable

data class BangumiGetUserSubjectEpisodeCollection200Response(

    @SerialName(value = "total") @Required val total: kotlin.Int,

    @SerialName(value = "limit") @Required val limit: kotlin.Int,

    @SerialName(value = "offset") @Required val offset: kotlin.Int,

    @SerialName(value = "data") val `data`: kotlin.collections.List<BangumiUserEpisodeCollection>? = null

) {


}

