/*
 * Copyright (C) 2024-2025 OpenAni and contributors.
 *
 * 此源代码的使用受 GNU AFFERO GENERAL PUBLIC LICENSE version 3 许可证的约束, 可以在以下链接找到该许可证.
 * Use of this source code is governed by the GNU AGPLv3 license, which can be found at the following link.
 *
 * https://github.com/open-ani/ani/blob/main/LICENSE
 */

/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport",
)

package me.him188.ani.datasources.bangumi.apis

import me.him188.ani.datasources.bangumi.models.BangumiCharacterDetail
import me.him188.ani.datasources.bangumi.models.BangumiCharacterPerson
import me.him188.ani.datasources.bangumi.models.BangumiCharacterRevision
import me.him188.ani.datasources.bangumi.models.BangumiDetailedRevision
import me.him188.ani.datasources.bangumi.models.BangumiEpType
import me.him188.ani.datasources.bangumi.models.BangumiEpisodeDetail
import me.him188.ani.datasources.bangumi.models.BangumiErrorDetail
import me.him188.ani.datasources.bangumi.models.BangumiGetUserSubjectEpisodeCollection200Response
import me.him188.ani.datasources.bangumi.models.BangumiIndex
import me.him188.ani.datasources.bangumi.models.BangumiIndexBasicInfo
import me.him188.ani.datasources.bangumi.models.BangumiIndexSubjectAddInfo
import me.him188.ani.datasources.bangumi.models.BangumiIndexSubjectEditInfo
import me.him188.ani.datasources.bangumi.models.BangumiPagedEpisode
import me.him188.ani.datasources.bangumi.models.BangumiPagedRevision
import me.him188.ani.datasources.bangumi.models.BangumiPagedUserCollection
import me.him188.ani.datasources.bangumi.models.BangumiPatchUserSubjectEpisodeCollectionRequest
import me.him188.ani.datasources.bangumi.models.BangumiPersonCharacter
import me.him188.ani.datasources.bangumi.models.BangumiPersonDetail
import me.him188.ani.datasources.bangumi.models.BangumiPersonRevision
import me.him188.ani.datasources.bangumi.models.BangumiPutUserEpisodeCollectionRequest
import me.him188.ani.datasources.bangumi.models.BangumiRelatedCharacter
import me.him188.ani.datasources.bangumi.models.BangumiRelatedPerson
import me.him188.ani.datasources.bangumi.models.BangumiSearchSubjects200Response
import me.him188.ani.datasources.bangumi.models.BangumiSearchSubjectsRequest
import me.him188.ani.datasources.bangumi.models.BangumiSubject
import me.him188.ani.datasources.bangumi.models.BangumiSubjectCollectionType
import me.him188.ani.datasources.bangumi.models.BangumiSubjectRevision
import me.him188.ani.datasources.bangumi.models.BangumiSubjectType
import me.him188.ani.datasources.bangumi.models.BangumiUser
import me.him188.ani.datasources.bangumi.models.BangumiUserEpisodeCollection
import me.him188.ani.datasources.bangumi.models.BangumiUserSubjectCollection
import me.him188.ani.datasources.bangumi.models.BangumiUserSubjectCollectionModifyPayload
import me.him188.ani.datasources.bangumi.models.BangumiV0RelatedSubject
import me.him188.ani.datasources.bangumi.models.BangumiV0SubjectRelation

import me.him188.ani.datasources.bangumi.infrastructure.*
import io.ktor.client.HttpClient
import io.ktor.client.HttpClientConfig
import io.ktor.client.request.forms.formData
import io.ktor.client.engine.HttpClientEngine
import kotlinx.serialization.json.Json
import io.ktor.http.ParametersBuilder
import kotlinx.serialization.*
import kotlinx.serialization.descriptors.*
import kotlinx.serialization.encoding.*

open class DefaultApi : ApiClient {

    constructor(
        baseUrl: String = ApiClient.BASE_URL,
        httpClientEngine: HttpClientEngine? = null,
        httpClientConfig: ((HttpClientConfig<*>) -> Unit)? = null,
        jsonSerializer: Json = ApiClient.JSON_DEFAULT
    ) : super(
        baseUrl = baseUrl,
        httpClientEngine = httpClientEngine,
        httpClientConfig = httpClientConfig,
        jsonBlock = jsonSerializer,
    )

    constructor(
        baseUrl: String,
        httpClient: HttpClient
    ) : super(baseUrl = baseUrl, httpClient = httpClient)

    /**
     * Add a subject to Index
     * 
     * @param indexId 目录 ID
     * @param bangumiIndexSubjectAddInfo  (optional)
     * @return void
     */
    open suspend fun addSubjectToIndexByIndexId(
        indexId: kotlin.Int,
        bangumiIndexSubjectAddInfo: BangumiIndexSubjectAddInfo? = null
    ): HttpResponse<Unit> {

        val localVariableAuthNames = listOf<String>("HTTPBearer")

        val localVariableBody = bangumiIndexSubjectAddInfo

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.POST,
            "/v0/indices/{index_id}/subjects".replace("{" + "index_id" + "}", "$indexId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return jsonRequest(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }



    /**
     * Collect index for current user
     * 为当前用户收藏一条目录
     * @param indexId 目录 ID
     * @return void
     */
    open suspend fun collectIndexByIndexIdAndUserId(indexId: kotlin.Int): HttpResponse<Unit> {

        val localVariableAuthNames = listOf<String>("HTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.POST,
            "/v0/indices/{index_id}/collect".replace("{" + "index_id" + "}", "$indexId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Delete a subject from a Index
     * 
     * @param indexId 目录 ID
     * @param subjectId 条目 ID
     * @return void
     */
    open suspend fun delelteSubjectFromIndexByIndexIdAndSubjectID(
        indexId: kotlin.Int,
        subjectId: kotlin.Int
    ): HttpResponse<Unit> {

        val localVariableAuthNames = listOf<String>("HTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.DELETE,
            "/v0/indices/{index_id}/subjects/{subject_id}".replace("{" + "index_id" + "}", "$indexId")
                .replace("{" + "subject_id" + "}", "$subjectId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Edit index&#39;s information
     * 
     * @param indexId 目录 ID
     * @param bangumiIndexBasicInfo  (optional)
     * @return BangumiIndex
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun editIndexById(
        indexId: kotlin.Int,
        bangumiIndexBasicInfo: BangumiIndexBasicInfo? = null
    ): HttpResponse<BangumiIndex> {

        val localVariableAuthNames = listOf<String>("HTTPBearer")

        val localVariableBody = bangumiIndexBasicInfo

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.PUT,
            "/v0/indices/{index_id}".replace("{" + "index_id" + "}", "$indexId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return jsonRequest(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }



    /**
     * Edit subject information in a index
     * 如果条目不存在于目录，会创建该条目
     * @param indexId 目录 ID
     * @param subjectId 条目 ID
     * @param bangumiIndexSubjectEditInfo  (optional)
     * @return void
     */
    open suspend fun editIndexSubjectsByIndexIdAndSubjectID(
        indexId: kotlin.Int,
        subjectId: kotlin.Int,
        bangumiIndexSubjectEditInfo: BangumiIndexSubjectEditInfo? = null
    ): HttpResponse<Unit> {

        val localVariableAuthNames = listOf<String>("HTTPBearer")

        val localVariableBody = bangumiIndexSubjectEditInfo

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.PUT,
            "/v0/indices/{index_id}/subjects/{subject_id}".replace("{" + "index_id" + "}", "$indexId")
                .replace("{" + "subject_id" + "}", "$subjectId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return jsonRequest(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }



    /**
     * Get Character Detail
     * cache with 60s
     * @param characterId 角色 ID
     * @return BangumiCharacterDetail
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getCharacterById(characterId: kotlin.Int): HttpResponse<BangumiCharacterDetail> {

        val localVariableAuthNames = listOf<String>()

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/characters/{character_id}".replace("{" + "character_id" + "}", "$characterId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get Character Image
     * 
     * @param characterId 角色 ID
     * @param type 枚举值 {small|grid|large|medium}
     * @return void
     */
    open suspend fun getCharacterImageById(characterId: kotlin.Int, type: kotlin.String): HttpResponse<Unit> {

        val localVariableAuthNames = listOf<String>("OptionalHTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        type?.apply { localVariableQuery["type"] = listOf("$type") }
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/characters/{character_id}/image".replace("{" + "character_id" + "}", "$characterId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get Character Revision
     * 
     * @param revisionId 版本 ID
     * @return BangumiCharacterRevision
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getCharacterRevisionByRevisionId(revisionId: kotlin.Int): HttpResponse<BangumiCharacterRevision> {

        val localVariableAuthNames = listOf<String>()

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/revisions/characters/{revision_id}".replace("{" + "revision_id" + "}", "$revisionId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get Character Revisions
     * 
     * @param characterId 角色 ID
     * @param limit 分页参数 (optional, default to 30)
     * @param offset 分页参数 (optional, default to 0)
     * @return BangumiPagedRevision
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getCharacterRevisions(
        characterId: kotlin.Int,
        limit: kotlin.Int? = 30,
        offset: kotlin.Int? = 0
    ): HttpResponse<BangumiPagedRevision> {

        val localVariableAuthNames = listOf<String>()

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        characterId?.apply { localVariableQuery["character_id"] = listOf("$characterId") }
        limit?.apply { localVariableQuery["limit"] = listOf("$limit") }
        offset?.apply { localVariableQuery["offset"] = listOf("$offset") }
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/revisions/characters",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get Episode
     * 
     * @param episodeId 章节 ID
     * @return BangumiEpisodeDetail
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getEpisodeById(episodeId: kotlin.Int): HttpResponse<BangumiEpisodeDetail> {

        val localVariableAuthNames = listOf<String>("OptionalHTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/episodes/{episode_id}".replace("{" + "episode_id" + "}", "$episodeId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get Episode Revision
     * 
     * @param revisionId 版本 ID
     * @return BangumiDetailedRevision
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getEpisodeRevisionByRevisionId(revisionId: kotlin.Int): HttpResponse<BangumiDetailedRevision> {

        val localVariableAuthNames = listOf<String>()

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/revisions/episodes/{revision_id}".replace("{" + "revision_id" + "}", "$revisionId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get Episode Revisions
     * 
     * @param episodeId 章节 ID
     * @param limit 分页参数 (optional, default to 30)
     * @param offset 分页参数 (optional, default to 0)
     * @return BangumiPagedRevision
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getEpisodeRevisions(
        episodeId: kotlin.Int,
        limit: kotlin.Int? = 30,
        offset: kotlin.Int? = 0
    ): HttpResponse<BangumiPagedRevision> {

        val localVariableAuthNames = listOf<String>()

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        episodeId?.apply { localVariableQuery["episode_id"] = listOf("$episodeId") }
        limit?.apply { localVariableQuery["limit"] = listOf("$limit") }
        offset?.apply { localVariableQuery["offset"] = listOf("$offset") }
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/revisions/episodes",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get Episodes
     * 
     * @param subjectId 条目 ID
     * @param type 参照章节的&#x60;type&#x60; (optional)
     * @param limit 分页参数 (optional, default to 100)
     * @param offset 分页参数 (optional, default to 0)
     * @return BangumiPagedEpisode
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getEpisodes(
        subjectId: kotlin.Int,
        type: BangumiEpType? = null,
        limit: kotlin.Int? = 100,
        offset: kotlin.Int? = 0
    ): HttpResponse<BangumiPagedEpisode> {

        val localVariableAuthNames = listOf<String>("OptionalHTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        subjectId?.apply { localVariableQuery["subject_id"] = listOf("$subjectId") }
        type?.apply { localVariableQuery["type"] = listOf("${type.value}") }
        limit?.apply { localVariableQuery["limit"] = listOf("$limit") }
        offset?.apply { localVariableQuery["offset"] = listOf("$offset") }
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/episodes",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get Index By ID
     * 
     * @param indexId 目录 ID
     * @return BangumiIndex
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getIndexById(indexId: kotlin.Int): HttpResponse<BangumiIndex> {

        val localVariableAuthNames = listOf<String>("OptionalHTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/indices/{index_id}".replace("{" + "index_id" + "}", "$indexId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get Index Subjects
     * 
     * @param indexId 目录 ID
     * @param type 条目类型 (optional)
     * @param limit 分页参数 (optional, default to 30)
     * @param offset 分页参数 (optional, default to 0)
     * @return void
     */
    open suspend fun getIndexSubjectsByIndexId(
        indexId: kotlin.Int,
        type: BangumiSubjectType? = null,
        limit: kotlin.Int? = 30,
        offset: kotlin.Int? = 0
    ): HttpResponse<Unit> {

        val localVariableAuthNames = listOf<String>("OptionalHTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        type?.apply { localVariableQuery["type"] = listOf("${type.value}") }
        limit?.apply { localVariableQuery["limit"] = listOf("$limit") }
        offset?.apply { localVariableQuery["offset"] = listOf("$offset") }
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/indices/{index_id}/subjects".replace("{" + "index_id" + "}", "$indexId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get User
     * 返回当前 Access Token 对应的用户信息
     * @return BangumiUser
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getMyself(): HttpResponse<BangumiUser> {

        val localVariableAuthNames = listOf<String>("HTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/me",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get Person
     * cache with 60s
     * @param personId 人物 ID
     * @return BangumiPersonDetail
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getPersonById(personId: kotlin.Int): HttpResponse<BangumiPersonDetail> {

        val localVariableAuthNames = listOf<String>()

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/persons/{person_id}".replace("{" + "person_id" + "}", "$personId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get Person Image
     * 
     * @param personId 人物 ID
     * @param type 枚举值 {small|grid|large|medium}
     * @return void
     */
    open suspend fun getPersonImageById(personId: kotlin.Int, type: kotlin.String): HttpResponse<Unit> {

        val localVariableAuthNames = listOf<String>("OptionalHTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        type?.apply { localVariableQuery["type"] = listOf("$type") }
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/persons/{person_id}/image".replace("{" + "person_id" + "}", "$personId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get Person Revision
     * 
     * @param revisionId 历史版本 ID
     * @return BangumiPersonRevision
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getPersonRevisionByRevisionId(revisionId: kotlin.Int): HttpResponse<BangumiPersonRevision> {

        val localVariableAuthNames = listOf<String>()

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/revisions/persons/{revision_id}".replace("{" + "revision_id" + "}", "$revisionId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get Person Revisions
     * 
     * @param personId 角色 ID
     * @param limit 分页参数 (optional, default to 30)
     * @param offset 分页参数 (optional, default to 0)
     * @return BangumiPagedRevision
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getPersonRevisions(
        personId: kotlin.Int,
        limit: kotlin.Int? = 30,
        offset: kotlin.Int? = 0
    ): HttpResponse<BangumiPagedRevision> {

        val localVariableAuthNames = listOf<String>()

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        personId?.apply { localVariableQuery["person_id"] = listOf("$personId") }
        limit?.apply { localVariableQuery["limit"] = listOf("$limit") }
        offset?.apply { localVariableQuery["offset"] = listOf("$offset") }
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/revisions/persons",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * get person related characters
     * 
     * @param personId 人物 ID
     * @return kotlin.collections.List<BangumiPersonCharacter>
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getRelatedCharactersByPersonId(personId: kotlin.Int): HttpResponse<kotlin.collections.List<BangumiPersonCharacter>> {

        val localVariableAuthNames = listOf<String>()

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/persons/{person_id}/characters".replace("{" + "person_id" + "}", "$personId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap<GetRelatedCharactersByPersonIdResponse>().map { value }
    }

    @Serializable(GetRelatedCharactersByPersonIdResponse.Companion::class)
    private class GetRelatedCharactersByPersonIdResponse(val value: List<BangumiPersonCharacter>) {
        companion object : KSerializer<GetRelatedCharactersByPersonIdResponse> {
            private val serializer: KSerializer<List<BangumiPersonCharacter>> =
                serializer<List<BangumiPersonCharacter>>()
            override val descriptor = serializer.descriptor
            override fun serialize(encoder: Encoder, value: GetRelatedCharactersByPersonIdResponse) =
                serializer.serialize(encoder, value.value)

            override fun deserialize(decoder: Decoder) =
                GetRelatedCharactersByPersonIdResponse(serializer.deserialize(decoder))
        }
    }

    /**
     * Get Subject Characters
     * 
     * @param subjectId 条目 ID
     * @return kotlin.collections.List<BangumiRelatedCharacter>
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getRelatedCharactersBySubjectId(subjectId: kotlin.Int): HttpResponse<kotlin.collections.List<BangumiRelatedCharacter>> {

        val localVariableAuthNames = listOf<String>("OptionalHTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/subjects/{subject_id}/characters".replace("{" + "subject_id" + "}", "$subjectId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap<GetRelatedCharactersBySubjectIdResponse>().map { value }
    }

    @Serializable(GetRelatedCharactersBySubjectIdResponse.Companion::class)
    private class GetRelatedCharactersBySubjectIdResponse(val value: List<BangumiRelatedCharacter>) {
        companion object : KSerializer<GetRelatedCharactersBySubjectIdResponse> {
            private val serializer: KSerializer<List<BangumiRelatedCharacter>> =
                serializer<List<BangumiRelatedCharacter>>()
            override val descriptor = serializer.descriptor
            override fun serialize(encoder: Encoder, value: GetRelatedCharactersBySubjectIdResponse) =
                serializer.serialize(encoder, value.value)

            override fun deserialize(decoder: Decoder) =
                GetRelatedCharactersBySubjectIdResponse(serializer.deserialize(decoder))
        }
    }

    /**
     * get character related persons
     * 
     * @param characterId 角色 ID
     * @return kotlin.collections.List<BangumiCharacterPerson>
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getRelatedPersonsByCharacterId(characterId: kotlin.Int): HttpResponse<kotlin.collections.List<BangumiCharacterPerson>> {

        val localVariableAuthNames = listOf<String>()

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/characters/{character_id}/persons".replace("{" + "character_id" + "}", "$characterId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap<GetRelatedPersonsByCharacterIdResponse>().map { value }
    }

    @Serializable(GetRelatedPersonsByCharacterIdResponse.Companion::class)
    private class GetRelatedPersonsByCharacterIdResponse(val value: List<BangumiCharacterPerson>) {
        companion object : KSerializer<GetRelatedPersonsByCharacterIdResponse> {
            private val serializer: KSerializer<List<BangumiCharacterPerson>> =
                serializer<List<BangumiCharacterPerson>>()
            override val descriptor = serializer.descriptor
            override fun serialize(encoder: Encoder, value: GetRelatedPersonsByCharacterIdResponse) =
                serializer.serialize(encoder, value.value)

            override fun deserialize(decoder: Decoder) =
                GetRelatedPersonsByCharacterIdResponse(serializer.deserialize(decoder))
        }
    }

    /**
     * Get Subject Persons
     * 
     * @param subjectId 条目 ID
     * @return kotlin.collections.List<BangumiRelatedPerson>
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getRelatedPersonsBySubjectId(subjectId: kotlin.Int): HttpResponse<kotlin.collections.List<BangumiRelatedPerson>> {

        val localVariableAuthNames = listOf<String>("OptionalHTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/subjects/{subject_id}/persons".replace("{" + "subject_id" + "}", "$subjectId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap<GetRelatedPersonsBySubjectIdResponse>().map { value }
    }

    @Serializable(GetRelatedPersonsBySubjectIdResponse.Companion::class)
    private class GetRelatedPersonsBySubjectIdResponse(val value: List<BangumiRelatedPerson>) {
        companion object : KSerializer<GetRelatedPersonsBySubjectIdResponse> {
            private val serializer: KSerializer<List<BangumiRelatedPerson>> = serializer<List<BangumiRelatedPerson>>()
            override val descriptor = serializer.descriptor
            override fun serialize(encoder: Encoder, value: GetRelatedPersonsBySubjectIdResponse) =
                serializer.serialize(encoder, value.value)

            override fun deserialize(decoder: Decoder) =
                GetRelatedPersonsBySubjectIdResponse(serializer.deserialize(decoder))
        }
    }

    /**
     * get character related subjects
     * 
     * @param characterId 角色 ID
     * @return kotlin.collections.List<BangumiV0RelatedSubject>
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getRelatedSubjectsByCharacterId(characterId: kotlin.Int): HttpResponse<kotlin.collections.List<BangumiV0RelatedSubject>> {

        val localVariableAuthNames = listOf<String>()

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/characters/{character_id}/subjects".replace("{" + "character_id" + "}", "$characterId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap<GetRelatedSubjectsByCharacterIdResponse>().map { value }
    }

    @Serializable(GetRelatedSubjectsByCharacterIdResponse.Companion::class)
    private class GetRelatedSubjectsByCharacterIdResponse(val value: List<BangumiV0RelatedSubject>) {
        companion object : KSerializer<GetRelatedSubjectsByCharacterIdResponse> {
            private val serializer: KSerializer<List<BangumiV0RelatedSubject>> =
                serializer<List<BangumiV0RelatedSubject>>()
            override val descriptor = serializer.descriptor
            override fun serialize(encoder: Encoder, value: GetRelatedSubjectsByCharacterIdResponse) =
                serializer.serialize(encoder, value.value)

            override fun deserialize(decoder: Decoder) =
                GetRelatedSubjectsByCharacterIdResponse(serializer.deserialize(decoder))
        }
    }

    /**
     * get person related subjects
     * 
     * @param personId 人物 ID
     * @return kotlin.collections.List<BangumiV0RelatedSubject>
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getRelatedSubjectsByPersonId(personId: kotlin.Int): HttpResponse<kotlin.collections.List<BangumiV0RelatedSubject>> {

        val localVariableAuthNames = listOf<String>()

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/persons/{person_id}/subjects".replace("{" + "person_id" + "}", "$personId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap<GetRelatedSubjectsByPersonIdResponse>().map { value }
    }

    @Serializable(GetRelatedSubjectsByPersonIdResponse.Companion::class)
    private class GetRelatedSubjectsByPersonIdResponse(val value: List<BangumiV0RelatedSubject>) {
        companion object : KSerializer<GetRelatedSubjectsByPersonIdResponse> {
            private val serializer: KSerializer<List<BangumiV0RelatedSubject>> =
                serializer<List<BangumiV0RelatedSubject>>()
            override val descriptor = serializer.descriptor
            override fun serialize(encoder: Encoder, value: GetRelatedSubjectsByPersonIdResponse) =
                serializer.serialize(encoder, value.value)

            override fun deserialize(decoder: Decoder) =
                GetRelatedSubjectsByPersonIdResponse(serializer.deserialize(decoder))
        }
    }

    /**
     * Get Subject Relations
     * 
     * @param subjectId 条目 ID
     * @return kotlin.collections.List<BangumiV0SubjectRelation>
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getRelatedSubjectsBySubjectId(subjectId: kotlin.Int): HttpResponse<kotlin.collections.List<BangumiV0SubjectRelation>> {

        val localVariableAuthNames = listOf<String>("OptionalHTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/subjects/{subject_id}/subjects".replace("{" + "subject_id" + "}", "$subjectId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap<GetRelatedSubjectsBySubjectIdResponse>().map { value }
    }

    @Serializable(GetRelatedSubjectsBySubjectIdResponse.Companion::class)
    private class GetRelatedSubjectsBySubjectIdResponse(val value: List<BangumiV0SubjectRelation>) {
        companion object : KSerializer<GetRelatedSubjectsBySubjectIdResponse> {
            private val serializer: KSerializer<List<BangumiV0SubjectRelation>> =
                serializer<List<BangumiV0SubjectRelation>>()
            override val descriptor = serializer.descriptor
            override fun serialize(encoder: Encoder, value: GetRelatedSubjectsBySubjectIdResponse) =
                serializer.serialize(encoder, value.value)

            override fun deserialize(decoder: Decoder) =
                GetRelatedSubjectsBySubjectIdResponse(serializer.deserialize(decoder))
        }
    }

    /**
     * 获取条目
     * cache with 300s
     * @param subjectId 条目 ID
     * @return BangumiSubject
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getSubjectById(subjectId: kotlin.Int): HttpResponse<BangumiSubject> {

        val localVariableAuthNames = listOf<String>("OptionalHTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/subjects/{subject_id}".replace("{" + "subject_id" + "}", "$subjectId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get Subject Image
     * 
     * @param subjectId 条目 ID
     * @param type 枚举值 {small|grid|large|medium|common}
     * @return void
     */
    open suspend fun getSubjectImageById(subjectId: kotlin.Int, type: kotlin.String): HttpResponse<Unit> {

        val localVariableAuthNames = listOf<String>("OptionalHTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        type?.apply { localVariableQuery["type"] = listOf("$type") }
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/subjects/{subject_id}/image".replace("{" + "subject_id" + "}", "$subjectId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get Subject Revision
     * 
     * @param revisionId 版本 ID
     * @return BangumiSubjectRevision
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getSubjectRevisionByRevisionId(revisionId: kotlin.Int): HttpResponse<BangumiSubjectRevision> {

        val localVariableAuthNames = listOf<String>()

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/revisions/subjects/{revision_id}".replace("{" + "revision_id" + "}", "$revisionId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get Subject Revisions
     * 
     * @param subjectId 条目 ID
     * @param limit 分页参数 (optional, default to 30)
     * @param offset 分页参数 (optional, default to 0)
     * @return BangumiPagedRevision
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getSubjectRevisions(
        subjectId: kotlin.Int,
        limit: kotlin.Int? = 30,
        offset: kotlin.Int? = 0
    ): HttpResponse<BangumiPagedRevision> {

        val localVariableAuthNames = listOf<String>()

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        subjectId?.apply { localVariableQuery["subject_id"] = listOf("$subjectId") }
        limit?.apply { localVariableQuery["limit"] = listOf("$limit") }
        offset?.apply { localVariableQuery["offset"] = listOf("$offset") }
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/revisions/subjects",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get User Avatar by name
     * 获取用户头像，302 重定向至头像地址，设置了 username 之后无法使用 UID 查询。
     * @param username 设置了用户名之后无法使用 UID。
     * @param type 枚举值 {small|large|medium}
     * @return void
     */
    open suspend fun getUserAvatarByName(username: kotlin.String, type: kotlin.String): HttpResponse<Unit> {

        val localVariableAuthNames = listOf<String>()

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        type?.apply { localVariableQuery["type"] = listOf("$type") }
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/users/{username}/avatar".replace("{" + "username" + "}", "$username"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Get User by name
     * 获取用户信息
     * @param username 设置了用户名之后无法使用 UID。
     * @return BangumiUser
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getUserByName(username: kotlin.String): HttpResponse<BangumiUser> {

        val localVariableAuthNames = listOf<String>()

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/users/{username}".replace("{" + "username" + "}", "$username"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * 获取用户单个收藏
     * 获取对应用户的收藏，查看私有收藏需要access token。
     * @param username 设置了用户名之后无法使用 UID。
     * @param subjectId 条目 ID
     * @return BangumiUserSubjectCollection
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getUserCollection(
        username: kotlin.String,
        subjectId: kotlin.Int
    ): HttpResponse<BangumiUserSubjectCollection> {

        val localVariableAuthNames = listOf<String>("OptionalHTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/users/{username}/collections/{subject_id}".replace("{" + "username" + "}", "$username")
                .replace("{" + "subject_id" + "}", "$subjectId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * 获取用户收藏
     * 获取对应用户的收藏，查看私有收藏需要access token。
     * @param username 设置了用户名之后无法使用 UID。
     * @param subjectType 条目类型，默认为全部  具体含义见 [SubjectType](#model-SubjectType) (optional)
     * @param type 收藏类型，默认为全部  具体含义见 [CollectionType](#model-CollectionType) (optional)
     * @param limit 分页参数 (optional, default to 30)
     * @param offset 分页参数 (optional, default to 0)
     * @return BangumiPagedUserCollection
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getUserCollectionsByUsername(
        username: kotlin.String,
        subjectType: BangumiSubjectType? = null,
        type: BangumiSubjectCollectionType? = null,
        limit: kotlin.Int? = 30,
        offset: kotlin.Int? = 0
    ): HttpResponse<BangumiPagedUserCollection> {

        val localVariableAuthNames = listOf<String>("OptionalHTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        subjectType?.apply { localVariableQuery["subject_type"] = listOf("${subjectType.value}") }
        type?.apply { localVariableQuery["type"] = listOf("${type.value}") }
        limit?.apply { localVariableQuery["limit"] = listOf("$limit") }
        offset?.apply { localVariableQuery["offset"] = listOf("$offset") }
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/users/{username}/collections".replace("{" + "username" + "}", "$username"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * 章节收藏信息
     * 
     * @param episodeId 章节 ID
     * @return BangumiUserEpisodeCollection
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getUserEpisodeCollection(episodeId: kotlin.Int): HttpResponse<BangumiUserEpisodeCollection> {

        val localVariableAuthNames = listOf<String>("HTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/users/-/collections/-/episodes/{episode_id}".replace("{" + "episode_id" + "}", "$episodeId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * 章节收藏信息
     * 
     * @param subjectId 条目 ID
     * @param offset 分页参数 (optional, default to 0)
     * @param limit 分页参数 (optional, default to 100)
     * @param episodeType 章节类型，不传则不按照章节进行筛选 (optional)
     * @return BangumiGetUserSubjectEpisodeCollection200Response
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun getUserSubjectEpisodeCollection(
        subjectId: kotlin.Int,
        offset: kotlin.Int? = 0,
        limit: kotlin.Int? = 100,
        episodeType: BangumiEpType? = null
    ): HttpResponse<BangumiGetUserSubjectEpisodeCollection200Response> {

        val localVariableAuthNames = listOf<String>("HTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        offset?.apply { localVariableQuery["offset"] = listOf("$offset") }
        limit?.apply { localVariableQuery["limit"] = listOf("$limit") }
        episodeType?.apply { localVariableQuery["episode_type"] = listOf("${episodeType.value}") }
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.GET,
            "/v0/users/-/collections/{subject_id}/episodes".replace("{" + "subject_id" + "}", "$subjectId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * Create a new index
     * 
     * @return BangumiIndex
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun newIndex(): HttpResponse<BangumiIndex> {

        val localVariableAuthNames = listOf<String>("HTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.POST,
            "/v0/indices",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


    /**
     * 修改用户单个收藏
     * 修改条目收藏状态  由于直接修改剧集条目的完成度可能会引起意料之外效果，只能用于修改书籍类条目的完成度。  PATCH 方法的所有请求体字段均可选 
     * @param subjectId 条目 ID
     * @param bangumiUserSubjectCollectionModifyPayload  (optional)
     * @return void
     */
    open suspend fun patchUserCollection(
        subjectId: kotlin.Int,
        bangumiUserSubjectCollectionModifyPayload: BangumiUserSubjectCollectionModifyPayload? = null
    ): HttpResponse<Unit> {

        val localVariableAuthNames = listOf<String>("OptionalHTTPBearer")

        val localVariableBody = bangumiUserSubjectCollectionModifyPayload

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.PATCH,
            "/v0/users/-/collections/{subject_id}".replace("{" + "subject_id" + "}", "$subjectId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return jsonRequest(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }



    /**
     * 章节收藏信息
     * 同时会重新计算条目的完成度 
     * @param subjectId 条目 ID
     * @param bangumiPatchUserSubjectEpisodeCollectionRequest  (optional)
     * @return void
     */
    open suspend fun patchUserSubjectEpisodeCollection(
        subjectId: kotlin.Int,
        bangumiPatchUserSubjectEpisodeCollectionRequest: BangumiPatchUserSubjectEpisodeCollectionRequest? = null
    ): HttpResponse<Unit> {

        val localVariableAuthNames = listOf<String>("HTTPBearer")

        val localVariableBody = bangumiPatchUserSubjectEpisodeCollectionRequest

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.PATCH,
            "/v0/users/-/collections/{subject_id}/episodes".replace("{" + "subject_id" + "}", "$subjectId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return jsonRequest(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }



    /**
     * 新增或修改用户单个收藏
     * 修改条目收藏状态, 如果不存在则创建，如果存在则修改  由于直接修改剧集条目的完成度可能会引起意料之外效果，只能用于修改书籍类条目的完成度。  方法的所有请求体字段均可选 
     * @param subjectId 条目 ID
     * @param bangumiUserSubjectCollectionModifyPayload  (optional)
     * @return void
     */
    open suspend fun postUserCollection(
        subjectId: kotlin.Int,
        bangumiUserSubjectCollectionModifyPayload: BangumiUserSubjectCollectionModifyPayload? = null
    ): HttpResponse<Unit> {

        val localVariableAuthNames = listOf<String>("OptionalHTTPBearer")

        val localVariableBody = bangumiUserSubjectCollectionModifyPayload

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.POST,
            "/v0/users/-/collections/{subject_id}".replace("{" + "subject_id" + "}", "$subjectId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return jsonRequest(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }



    /**
     * 更新章节收藏信息
     * 
     * @param episodeId 章节 ID
     * @param bangumiPutUserEpisodeCollectionRequest  (optional)
     * @return void
     */
    open suspend fun putUserEpisodeCollection(
        episodeId: kotlin.Int,
        bangumiPutUserEpisodeCollectionRequest: BangumiPutUserEpisodeCollectionRequest? = null
    ): HttpResponse<Unit> {

        val localVariableAuthNames = listOf<String>("HTTPBearer")

        val localVariableBody = bangumiPutUserEpisodeCollectionRequest

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.PUT,
            "/v0/users/-/collections/-/episodes/{episode_id}".replace("{" + "episode_id" + "}", "$episodeId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return jsonRequest(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }



    /**
     * 条目搜索
     * ## 实验性 API， 本 schema 和实际的 API 行为都可能随时发生改动  目前支持的筛选条件包括: - &#x60;type&#x60;: 条目类型，参照 &#x60;SubjectType&#x60; enum， &#x60;或&#x60;。 - &#x60;tag&#x60;: 标签，可以多次出现。&#x60;且&#x60; 关系。 - &#x60;airdate&#x60;: 播出日期/发售日期。&#x60;且&#x60; 关系。 - &#x60;rating&#x60;: 用于搜索指定评分的条目。&#x60;且&#x60; 关系。 - &#x60;rank&#x60;: 用于搜索指定排名的条目。&#x60;且&#x60; 关系。 - &#x60;nsfw&#x60;: 使用 &#x60;include&#x60; 包含NSFW搜索结果。默认排除搜索NSFW条目。无权限情况下忽略此选项，不会返回NSFW条目。  不同筛选条件之间为 &#x60;且&#x60;   由于目前 meilisearch 的一些问题，条目排名更新并不会触发搜索数据更新，所以条目排名可能是过期数据。  希望未来版本的 meilisearch 能解决相关的问题。 
     * @param limit 分页参数 (optional)
     * @param offset 分页参数 (optional)
     * @param bangumiSearchSubjectsRequest  (optional)
     * @return BangumiSearchSubjects200Response
     */
    @Suppress("UNCHECKED_CAST")
    open suspend fun searchSubjects(
        limit: kotlin.Int? = null,
        offset: kotlin.Int? = null,
        bangumiSearchSubjectsRequest: BangumiSearchSubjectsRequest? = null
    ): HttpResponse<BangumiSearchSubjects200Response> {

        val localVariableAuthNames = listOf<String>()

        val localVariableBody = bangumiSearchSubjectsRequest

        val localVariableQuery = mutableMapOf<String, List<String>>()
        limit?.apply { localVariableQuery["limit"] = listOf("$limit") }
        offset?.apply { localVariableQuery["offset"] = listOf("$offset") }
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.POST,
            "/v0/search/subjects",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
        )

        return jsonRequest(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }



    /**
     * Uncollect index for current user
     * 为当前用户取消收藏一条目录
     * @param indexId 目录 ID
     * @return void
     */
    open suspend fun uncollectIndexByIndexIdAndUserId(indexId: kotlin.Int): HttpResponse<Unit> {

        val localVariableAuthNames = listOf<String>("HTTPBearer")

        val localVariableBody = 
            io.ktor.client.utils.EmptyContent

        val localVariableQuery = mutableMapOf<String, List<String>>()
        val localVariableHeaders = mutableMapOf<String, String>()

        val localVariableConfig = RequestConfig<kotlin.Any?>(
            RequestMethod.DELETE,
            "/v0/indices/{index_id}/collect".replace("{" + "index_id" + "}", "$indexId"),
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = true,
        )

        return request(
            localVariableConfig,
            localVariableBody,
            localVariableAuthNames,
        ).wrap()
    }


}
