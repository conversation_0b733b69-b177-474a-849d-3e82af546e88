/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport",
)

package me.him188.ani.datasources.bangumi.next.models

import kotlinx.serialization.Required
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 
 *
 * @param comment 
 * @param rate 
 * @param tags 
 * @param type 
 * @param updatedAt 
 */
@Serializable

data class BangumiNextSlimSubjectInterest(

    @SerialName(value = "comment") @Required val comment: kotlin.String,

    @SerialName(value = "rate") @Required val rate: kotlin.Int,

    @SerialName(value = "tags") @Required val tags: kotlin.collections.List<kotlin.String>,

    @SerialName(value = "type") @Required val type: BangumiNextCollectionType,

    @SerialName(value = "updatedAt") @Required val updatedAt: kotlin.Int

) {


}

