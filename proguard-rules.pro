-dontwarn okio.AsyncTimeout$Watchdog

# BouncyCastle ProGuard Rules
# Keep all BouncyCastle classes to prevent SHA-256 digest errors
# BouncyCastle is a signed JAR and ProGuard modifications break the signatures
-keep class org.bouncycastle.** { *; }
-dontwarn org.bouncycastle.**

# Keep BouncyCastle provider specifically
-keep class org.bouncycastle.jce.provider.BouncyCastleProvider { *; }

# Keep cryptographic interfaces and implementations
-keep class * extends java.security.Provider { *; }
-keep class * implements java.security.interfaces.** { *; }

# Keep security-related classes that might be used by BouncyCastle
-keepclassmembers class * {
    @javax.crypto.* *;
}

# Additional rules for ECDH and cryptographic operations
-keep class javax.crypto.** { *; }
-keep class java.security.** { *; }
-dontwarn javax.crypto.**
-dontwarn java.security.**

# Keep your ECDHUtils class and its methods
-keep class dev.hikari.xlogdecoder.ECDHUtils { *; }

# Keep Security provider registration methods
-keepclassmembers class java.security.Security {
    public static void insertProviderAt(...);
    public static int addProvider(...);
}