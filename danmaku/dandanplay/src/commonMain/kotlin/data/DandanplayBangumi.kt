package me.him188.ani.danmaku.dandanplay.data

import kotlinx.serialization.Serializable

/*
 * Generated by ChatGPT from https://api.dandanplay.net/swagger/ui/index#!/Bangumi/Bangumi_GetBangumiDetails
 */

/**
 * Bangumi Details Response Model
 * @property bangumi 番剧详情
 * @property errorCode 错误代码，0表示没有发生错误，非0表示有错误，详细信息会包含在errorMessage属性中
 * @property success 接口是否调用成功
 * @property errorMessage 当发生错误时，说明错误具体原因
 */
@Serializable
data class DandanplayBangumiDetailsResponse(
    val bangumi: DandanplayBangumiDetails? = null,
    val errorCode: Int,
    val success: Boolean,
    val errorMessage: String? = null
)

/**
 * Bangumi Details Model
 * @property type 作品类型 = ['tvseries', 'tvspecial', 'ova', 'movie', 'musicvideo', 'web', 'other', 'jpmovie', 'jpdrama', 'unknown']
 * @property typeDescription 类型描述
 * @property titles 作品标题
 * @property episodes 剧集列表
 * @property summary 番剧简介
 * @property metadata 番剧元数据（名称、制作人员、配音人员等）
 * @property bangumiUrl Bangumi.tv页面地址
 * @property userRating 用户个人评分（0-10）
 * @property favoriteStatus 关注状态 = ['favorited', 'finished', 'abandoned']
 * @property comment 用户对此番剧的备注/评论/标签
 * @property ratingDetails 各个站点的评分详情
 * @property relateds 与此作品直接关联的其他作品（例如同一作品的不同季、剧场版、OVA等）
 * @property similars 与此作品相似的其他作品
 * @property tags 标签列表
 * @property onlineDatabases 此作品在其他在线数据库/网站的对应url
 * @property animeId 作品编号
 * @property animeTitle 作品标题
 * @property imageUrl 海报图片地址
 * @property searchKeyword 搜索关键词
 * @property isOnAir 是否正在连载中
 * @property airDay 周几上映，0代表周日，1-6代表周一至周六
 * @property isFavorited 当前用户是否已关注（无论是否为已弃番等附加状态）
 * @property isRestricted 是否为限制级别的内容（例如属于R18分级）
 * @property rating 番剧综合评分（综合多个来源的评分求出的加权平均值，0-10分）
 */
@Serializable
data class DandanplayBangumiDetails(
    val type: String,
    val typeDescription: String? = null,
    val titles: List<DandanplayBangumiTitle>? = null,
    val episodes: List<DandanplayBangumiEpisode>? = null,
    val summary: String? = null,
    val metadata: List<String>? = null,
//    val bangumiUrl: String? = null,
//    val userRating: Int,
//    val favoriteStatus: String,
//    val comment: String? = null,
//    val ratingDetails: DandanplayInlineModel? = null,
//    val relateds: List<DandanplayBangumiIntro>? = null,
//    val similars: List<DandanplayBangumiIntro>? = null,
//    val tags: List<DandanplayBangumiTag>? = null,
//    val onlineDatabases: List<DandanplayBangumiOnlineDatabase>? = null,
//    val animeId: Int,
    val animeTitle: String? = null,
//    val imageUrl: String? = null,
//    val searchKeyword: String? = null,
//    val isOnAir: Boolean,
//    val airDay: Int,
//    val isFavorited: Boolean,
//    val isRestricted: Boolean,
//    val rating: Double
)

/**
 * Bangumi Title Model
 * @property language 语言
 * @property title 标题
 */
@Serializable
data class DandanplayBangumiTitle(
    val language: String? = null,
    val title: String? = null
)

/**
 * Bangumi Episode Model
 * @property episodeId 剧集ID（弹幕库编号）
 * @property episodeTitle 剧集完整标题
 * @property episodeNumber 剧集短标题（可以用来排序，非纯数字，可能包含字母）
 * @property lastWatched 上次观看时间（服务器时间，即北京时间）
 * @property airDate 本集上映时间（当地时间）
 */
@Serializable
data class DandanplayBangumiEpisode(
    val episodeId: Int,
    val episodeTitle: String,
    val episodeNumber: String?,
    val lastWatched: String?,
    val airDate: String?
)

/**
 * Inline Model for Rating Details
 */
@Serializable
class DandanplayInlineModel

/**
 * Bangumi Intro Model
 * @property animeId 作品编号
 * @property animeTitle 作品标题
 * @property imageUrl 海报图片地址
 * @property searchKeyword 搜索关键词
 * @property isOnAir 是否正在连载中
 * @property airDay 周几上映，0代表周日，1-6代表周一至周六
 * @property isFavorited 当前用户是否已关注（无论是否为已弃番等附加状态）
 * @property isRestricted 是否为限制级别的内容（例如属于R18分级）
 * @property rating 番剧综合评分（综合多个来源的评分求出的加权平均值，0-10分）
 */
@Serializable
data class DandanplayBangumiIntro(
    val animeId: Int,
    val animeTitle: String? = null,
    val imageUrl: String? = null,
    val searchKeyword: String? = null,
    val isOnAir: Boolean,
    val airDay: Int,
    val isFavorited: Boolean,
    val isRestricted: Boolean,
    val rating: Double
)

/**
 * Bangumi Tag Model
 * @property id 标签编号
 * @property name 标签内容
 * @property count 观众为此标签+1次数
 */
@Serializable
data class DandanplayBangumiTag(
    val id: Int,
    val name: String? = null,
    val count: Int
)

/**
 * Bangumi Online Database Model
 * @property name 网站名称
 * @property url 网址
 */
@Serializable
data class DandanplayBangumiOnlineDatabase(
    val name: String? = null,
    val url: String? = null
)
