/*
 * Copyright (C) 2024-2025 OpenAni and contributors.
 *
 * 此源代码的使用受 GNU AFFERO GENERAL PUBLIC LICENSE version 3 许可证的约束, 可以在以下链接找到该许可证.
 * Use of this source code is governed by the GNU AGPLv3 license, which can be found at the following link.
 *
 * https://github.com/open-ani/ani/blob/main/LICENSE
 */

package me.him188.ani.danmaku.api

object DanmakuSanitizer {
    fun sanitize(danmaku: DanmakuInfo): DanmakuInfo = danmaku.run {
        if (text.indexOf("\n") == -1) return@run this

        copy(
            content = content.copy(
                text = text
                    .replace("\n\r", " ")
                    .replace("\r\n", " ")
                    .replace("\n", " ")
                    .trim(),
            ),
        )
    }
}
