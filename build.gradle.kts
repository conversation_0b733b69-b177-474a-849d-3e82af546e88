import org.jetbrains.compose.desktop.application.dsl.TargetFormat
import java.io.FileOutputStream
import java.util.*

plugins {
    kotlin("jvm")
    id("org.jetbrains.compose")
    id("org.jetbrains.kotlin.plugin.compose")
}

group = "dev.hikari"
version = "1.0.5"

val generatedVersionDir = "${layout.buildDirectory.get()}/generated-version"
sourceSets {
    main {
        kotlin {
            output.dir(generatedVersionDir)
        }
    }
}

tasks.register("generateVersionProperties") {
    doLast {
        val propertiesFile = file("$generatedVersionDir/version.properties")
        propertiesFile.parentFile.mkdirs()
        val properties = Properties()
        properties.setProperty("version", "$version")
        val out = FileOutputStream(propertiesFile)
        properties.store(out, null)
    }
}

tasks.named("processResources") {
    dependsOn("generateVersionProperties")
}

repositories {
    mavenCentral()
    maven("https://maven.pkg.jetbrains.space/public/p/compose/dev")
    google()
}

dependencies {
    // Note, if you develop a library, you should use compose.desktop.common.
    // compose.desktop.currentOs should be used in launcher-sourceSet
    // (in a separate module for demo project and in testMain).
    // With compose.desktop.common you will also lose @Preview functionality
    implementation(compose.desktop.currentOs)
    implementation(compose.material3)
//    implementation(compose.materialIconsExtended)
    implementation("org.bouncycastle:bcprov-jdk18on:1.81")

    implementation("net.harawata:appdirs:1.4.0")
    val dataStoreVersion = "1.1.7"
    implementation("androidx.datastore:datastore:$dataStoreVersion")
    implementation("androidx.datastore:datastore-preferences:$dataStoreVersion")
}

compose.desktop {
    application {
        mainClass = "dev.hikari.xlogdecoder.MainKt"

        nativeDistributions {
            targetFormats(TargetFormat.Dmg, TargetFormat.Msi, TargetFormat.Deb)
            packageName = "XLogDecoder"
            packageVersion = version.toString()
            modules("jdk.unsupported")

            windows {
                shortcut = true
            }
        }

        buildTypes.release.proguard {
            version = "7.7.0"
            isEnabled.set(true)
            optimize.set(true)
            obfuscate.set(false)
            configurationFiles.from(file("./proguard-rules.pro"))
        }
    }
}
